import React, { useState } from 'react';
import { Save, RefreshCw } from 'lucide-react';
import { InvestmentCriteria } from '../types';
import { defaultInvestmentCriteria } from '../utils/mockData';

interface SettingsProps {
  criteria: InvestmentCriteria;
  onCriteriaChange: (criteria: InvestmentCriteria) => void;
}

export const Settings: React.FC<SettingsProps> = ({ criteria, onCriteriaChange }) => {
  const [localCriteria, setLocalCriteria] = useState<InvestmentCriteria>(criteria);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    onCriteriaChange(localCriteria);
    setIsSaving(false);
  };

  const handleReset = () => {
    setLocalCriteria(defaultInvestmentCriteria);
  };

  const handleInputChange = (field: keyof InvestmentCriteria, value: number) => {
    setLocalCriteria(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900">Investment Criteria</h2>
        <p className="text-gray-600 mt-1">Configure your buy box parameters</p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Financial Criteria */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Criteria</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Cash-on-Cash Return (%)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={localCriteria.minCashOnCash}
                    onChange={(e) => handleInputChange('minCashOnCash', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Cap Rate (%)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={localCriteria.minCapRate}
                    onChange={(e) => handleInputChange('minCapRate', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum Cap Rate (%)
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={localCriteria.maxCapRate}
                    onChange={(e) => handleInputChange('maxCapRate', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum DSCR
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={localCriteria.minDscr}
                    onChange={(e) => handleInputChange('minDscr', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Property Criteria */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Property Criteria</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Year Built
                  </label>
                  <input
                    type="number"
                    value={localCriteria.minYearBuilt}
                    onChange={(e) => handleInputChange('minYearBuilt', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum Purchase Price ($)
                  </label>
                  <input
                    type="number"
                    value={localCriteria.maxPurchasePrice}
                    onChange={(e) => handleInputChange('maxPurchasePrice', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Target Hold Period (years)
                  </label>
                  <input
                    type="number"
                    value={localCriteria.targetHoldPeriod}
                    onChange={(e) => handleInputChange('targetHoldPeriod', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {/* Market Criteria */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Market Criteria</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum Crime Score
                  </label>
                  <input
                    type="number"
                    value={localCriteria.maxCrimeScore}
                    onChange={(e) => handleInputChange('maxCrimeScore', parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">Lower scores indicate safer areas</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum School Score
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={localCriteria.minSchoolScore}
                    onChange={(e) => handleInputChange('minSchoolScore', parseFloat(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <p className="text-xs text-gray-500 mt-1">Scale of 1-10, higher is better</p>
                </div>
              </div>
            </div>

            {/* Current Values Display */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Current Settings Summary</h3>
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Min CoC:</span> {localCriteria.minCashOnCash}%
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Cap Rate Range:</span> {localCriteria.minCapRate}% - {localCriteria.maxCapRate}%
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Min DSCR:</span> {localCriteria.minDscr}
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Min Year Built:</span> {localCriteria.minYearBuilt}
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Max Price:</span> ${localCriteria.maxPurchasePrice.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Hold Period:</span> {localCriteria.targetHoldPeriod} years
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
          <button
            onClick={handleReset}
            className="flex items-center px-4 py-2 text-gray-600 hover:text-gray-900 transition-colors"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset to Defaults
          </button>
          
          <button
            onClick={handleSave}
            disabled={isSaving}
            className="flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </button>
        </div>
      </div>
    </div>
  );
};