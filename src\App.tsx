import React, { useState } from 'react';
import { Header } from './components/Header';
import { Dashboard } from './components/Dashboard';
import { NewAnalysis } from './components/NewAnalysis';
import { DealHistory } from './components/DealHistory';
import { Settings } from './components/Settings';
import { Deal, InvestmentCriteria } from './types';
import { defaultInvestmentCriteria } from './utils/mockData';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [analyzedDeals, setAnalyzedDeals] = useState<Deal[]>([]);
  const [investmentCriteria, setInvestmentCriteria] = useState<InvestmentCriteria>(defaultInvestmentCriteria);

  const handleAnalysisComplete = (deal: Deal) => {
    setAnalyzedDeals(prev => [deal, ...prev]);
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'analyze':
        return <NewAnalysis onAnalysisComplete={handleAnalysisComplete} />;
      case 'history':
        return <DealHistory deals={analyzedDeals} />;
      case 'settings':
        return (
          <Settings 
            criteria={investmentCriteria} 
            onCriteriaChange={setInvestmentCriteria} 
          />
        );
      default:
        return <Dashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Header activeTab={activeTab} onTabChange={setActiveTab} />
      <main>
        {renderContent()}
      </main>
    </div>
  );
}

export default App;