export interface Property {
  id: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  propertyType: 'multifamily' | 'office' | 'retail' | 'industrial';
  units?: number;
  squareFeet: number;
  yearBuilt: number;
  createdAt: string;
}

export interface FinancialData {
  grossRent: number;
  operatingExpenses: number;
  netOperatingIncome: number;
  capRate: number;
  cashOnCashReturn: number;
  dscr: number;
  purchasePrice: number;
  downPayment: number;
  loanAmount: number;
}

export interface MarketData {
  averageRentPsf: number;
  occupancyRate: number;
  crimeScore: number;
  schoolScore: number;
  walkScore: number;
  medianIncome: number;
  populationGrowth: number;
}

export interface InvestmentCriteria {
  minCashOnCash: number;
  minCapRate: number;
  maxCapRate: number;
  minYearBuilt: number;
  maxPurchasePrice: number;
  targetHoldPeriod: number;
  minDscr: number;
  maxCrimeScore: number;
  minSchoolScore: number;
}

export interface Deal {
  id: string;
  property: Property;
  financials: FinancialData;
  marketData: MarketData;
  recommendation: 'pass' | 'fail';
  riskScore: number;
  analysisDate: string;
  timeToAnalyze: number; // in seconds
}

export interface User {
  id: string;
  name: string;
  email: string;
  company: string;
  dealsAnalyzed: number;
  timeSaved: number; // in hours
}