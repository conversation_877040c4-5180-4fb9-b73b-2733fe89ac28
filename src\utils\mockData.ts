import { Deal, InvestmentCriteria, MarketData, Property, FinancialData } from '../types';

export const defaultInvestmentCriteria: InvestmentCriteria = {
  minCashOnCash: 8.0,
  minCapRate: 5.0,
  maxCapRate: 12.0,
  minYearBuilt: 1980,
  maxPurchasePrice: 10000000,
  targetHoldPeriod: 5,
  minDscr: 1.2,
  maxCrimeScore: 50,
  minSchoolScore: 6,
};

export const mockDeals: Deal[] = [
  {
    id: '1',
    property: {
      id: 'prop1',
      address: '123 Main Street',
      city: 'Austin',
      state: 'TX',
      zipCode: '78701',
      propertyType: 'multifamily',
      units: 48,
      squareFeet: 45000,
      yearBuilt: 2015,
      createdAt: '2024-01-15T10:30:00Z',
    },
    financials: {
      grossRent: 720000,
      operatingExpenses: 288000,
      netOperatingIncome: 432000,
      capRate: 5.4,
      cashOnCashReturn: 9.2,
      dscr: 1.45,
      purchasePrice: 8000000,
      downPayment: 2000000,
      loanAmount: 6000000,
    },
    marketData: {
      averageRentPsf: 16.0,
      occupancyRate: 94.5,
      crimeScore: 25,
      schoolScore: 8.5,
      walkScore: 78,
      medianIncome: 85000,
      populationGrowth: 2.8,
    },
    recommendation: 'pass',
    riskScore: 15,
    analysisDate: '2024-01-15T10:32:45Z',
    timeToAnalyze: 28,
  },
  {
    id: '2',
    property: {
      id: 'prop2',
      address: '456 Oak Avenue',
      city: 'Dallas',
      state: 'TX',
      zipCode: '75201',
      propertyType: 'multifamily',
      units: 72,
      squareFeet: 68000,
      yearBuilt: 1995,
      createdAt: '2024-01-14T14:20:00Z',
    },
    financials: {
      grossRent: 864000,
      operatingExpenses: 432000,
      netOperatingIncome: 432000,
      capRate: 4.8,
      cashOnCashReturn: 6.5,
      dscr: 1.25,
      purchasePrice: 9000000,
      downPayment: 2250000,
      loanAmount: 6750000,
    },
    marketData: {
      averageRentPsf: 12.7,
      occupancyRate: 89.2,
      crimeScore: 42,
      schoolScore: 7.2,
      walkScore: 65,
      medianIncome: 72000,
      populationGrowth: 1.9,
    },
    recommendation: 'fail',
    riskScore: 35,
    analysisDate: '2024-01-14T14:22:15Z',
    timeToAnalyze: 32,
  },
  {
    id: '3',
    property: {
      id: 'prop3',
      address: '789 Pine Street',
      city: 'Houston',
      state: 'TX',
      zipCode: '77002',
      propertyType: 'multifamily',
      units: 96,
      squareFeet: 85000,
      yearBuilt: 2018,
      createdAt: '2024-01-13T09:45:00Z',
    },
    financials: {
      grossRent: 1152000,
      operatingExpenses: 460800,
      netOperatingIncome: 691200,
      capRate: 6.1,
      cashOnCashReturn: 11.8,
      dscr: 1.62,
      purchasePrice: 11300000,
      downPayment: 2825000,
      loanAmount: 8475000,
    },
    marketData: {
      averageRentPsf: 13.5,
      occupancyRate: 96.8,
      crimeScore: 18,
      schoolScore: 9.1,
      walkScore: 82,
      medianIncome: 92000,
      populationGrowth: 3.2,
    },
    recommendation: 'pass',
    riskScore: 8,
    analysisDate: '2024-01-13T09:47:22Z',
    timeToAnalyze: 25,
  },
];

export const generateMockAnalysis = (address: string): Deal => {
  const id = Date.now().toString();
  const analysisTime = Math.floor(Math.random() * 15) + 20; // 20-35 seconds
  
  // Generate random but realistic data
  const units = Math.floor(Math.random() * 80) + 20;
  const squareFeet = units * (Math.floor(Math.random() * 400) + 600);
  const yearBuilt = Math.floor(Math.random() * 30) + 1990;
  const purchasePrice = (Math.floor(Math.random() * 8000) + 2000) * 1000;
  const grossRent = Math.floor(purchasePrice * (Math.random() * 0.04 + 0.06));
  const operatingExpenses = Math.floor(grossRent * (Math.random() * 0.2 + 0.3));
  const noi = grossRent - operatingExpenses;
  const capRate = (noi / purchasePrice) * 100;
  const downPayment = Math.floor(purchasePrice * 0.25);
  const loanAmount = purchasePrice - downPayment;
  const cashOnCash = (noi - (loanAmount * 0.06)) / downPayment * 100;
  const dscr = noi / (loanAmount * 0.06);
  
  const riskScore = Math.floor(Math.random() * 40) + 5;
  const recommendation = capRate >= 5.0 && cashOnCash >= 8.0 && dscr >= 1.2 ? 'pass' : 'fail';
  
  return {
    id,
    property: {
      id: `prop${id}`,
      address,
      city: 'Austin',
      state: 'TX',
      zipCode: '78701',
      propertyType: 'multifamily',
      units,
      squareFeet,
      yearBuilt,
      createdAt: new Date().toISOString(),
    },
    financials: {
      grossRent,
      operatingExpenses,
      netOperatingIncome: noi,
      capRate: Math.round(capRate * 10) / 10,
      cashOnCashReturn: Math.round(cashOnCash * 10) / 10,
      dscr: Math.round(dscr * 100) / 100,
      purchasePrice,
      downPayment,
      loanAmount,
    },
    marketData: {
      averageRentPsf: Math.round((Math.random() * 8 + 12) * 10) / 10,
      occupancyRate: Math.round((Math.random() * 15 + 85) * 10) / 10,
      crimeScore: Math.floor(Math.random() * 60) + 10,
      schoolScore: Math.round((Math.random() * 4 + 6) * 10) / 10,
      walkScore: Math.floor(Math.random() * 40) + 40,
      medianIncome: Math.floor(Math.random() * 40000) + 60000,
      populationGrowth: Math.round((Math.random() * 3 + 1) * 10) / 10,
    },
    recommendation: recommendation as 'pass' | 'fail',
    riskScore,
    analysisDate: new Date().toISOString(),
    timeToAnalyze: analysisTime,
  };
};