import React, { useState } from 'react';
import { Upload, MapPin, FileText, BarChart3, Download, ExternalLink } from 'lucide-react';
import { Deal } from '../types';
import { generateMockAnalysis } from '../utils/mockData';

interface NewAnalysisProps {
  onAnalysisComplete: (deal: Deal) => void;
}

export const NewAnalysis: React.FC<NewAnalysisProps> = ({ onAnalysisComplete }) => {
  const [address, setAddress] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentDeal, setCurrentDeal] = useState<Deal | null>(null);
  const [analysisStep, setAnalysisStep] = useState(0);

  const analysisSteps = [
    'Extracting property data...',
    'Parsing T12 financials...',
    'Analyzing rent roll...',
    'Pulling market comps...',
    'Calculating risk metrics...',
    'Generating recommendations...'
  ];

  const handleAnalysis = async () => {
    if (!address.trim()) return;
    
    setIsAnalyzing(true);
    setAnalysisStep(0);
    
    // Simulate real-time analysis progress
    for (let i = 0; i < analysisSteps.length; i++) {
      setAnalysisStep(i);
      await new Promise(resolve => setTimeout(resolve, Math.random() * 3000 + 2000));
    }
    
    const deal = generateMockAnalysis(address);
    setCurrentDeal(deal);
    onAnalysisComplete(deal);
    setIsAnalyzing(false);
  };

  const handleNewAnalysis = () => {
    setCurrentDeal(null);
    setAddress('');
    setAnalysisStep(0);
  };

  if (currentDeal) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6 flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Analysis Results</h2>
            <p className="text-gray-600">{currentDeal.property.address}</p>
          </div>
          <button
            onClick={handleNewAnalysis}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            New Analysis
          </button>
        </div>

        {/* Recommendation Banner */}
        <div className={`p-6 rounded-lg mb-6 ${
          currentDeal.recommendation === 'pass' 
            ? 'bg-green-50 border border-green-200' 
            : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className={`text-lg font-semibold ${
                currentDeal.recommendation === 'pass' ? 'text-green-800' : 'text-red-800'
              }`}>
                {currentDeal.recommendation === 'pass' ? '✅ RECOMMENDED' : '❌ NOT RECOMMENDED'}
              </h3>
              <p className={`text-sm ${
                currentDeal.recommendation === 'pass' ? 'text-green-600' : 'text-red-600'
              }`}>
                {currentDeal.recommendation === 'pass' 
                  ? 'This deal meets your investment criteria' 
                  : 'This deal does not meet your investment criteria'}
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Risk Score</p>
              <p className={`text-2xl font-bold ${
                currentDeal.riskScore < 20 ? 'text-green-600' : 
                currentDeal.riskScore < 40 ? 'text-yellow-600' : 'text-red-600'
              }`}>
                {currentDeal.riskScore}
              </p>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-sm font-medium text-gray-600 mb-2">Cap Rate</h4>
            <p className="text-3xl font-bold text-gray-900">{currentDeal.financials.capRate}%</p>
            <p className="text-sm text-gray-500 mt-1">Target: 5.0%+</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-sm font-medium text-gray-600 mb-2">Cash-on-Cash Return</h4>
            <p className="text-3xl font-bold text-gray-900">{currentDeal.financials.cashOnCashReturn}%</p>
            <p className="text-sm text-gray-500 mt-1">Target: 8.0%+</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-sm font-medium text-gray-600 mb-2">DSCR</h4>
            <p className="text-3xl font-bold text-gray-900">{currentDeal.financials.dscr}</p>
            <p className="text-sm text-gray-500 mt-1">Target: 1.2+</p>
          </div>
        </div>

        {/* Detailed Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Property Details */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Property Details</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Property Type</span>
                <span className="font-medium text-gray-900 capitalize">
                  {currentDeal.property.propertyType}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Units</span>
                <span className="font-medium text-gray-900">{currentDeal.property.units}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Square Feet</span>
                <span className="font-medium text-gray-900">
                  {currentDeal.property.squareFeet.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Year Built</span>
                <span className="font-medium text-gray-900">{currentDeal.property.yearBuilt}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Purchase Price</span>
                <span className="font-medium text-gray-900">
                  ${currentDeal.financials.purchasePrice.toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          {/* Financial Summary */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Financial Summary</h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Gross Rent</span>
                <span className="font-medium text-gray-900">
                  ${currentDeal.financials.grossRent.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Operating Expenses</span>
                <span className="font-medium text-gray-900">
                  ${currentDeal.financials.operatingExpenses.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Net Operating Income</span>
                <span className="font-medium text-gray-900">
                  ${currentDeal.financials.netOperatingIncome.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Down Payment</span>
                <span className="font-medium text-gray-900">
                  ${currentDeal.financials.downPayment.toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Loan Amount</span>
                <span className="font-medium text-gray-900">
                  ${currentDeal.financials.loanAmount.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Market Data */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Market Analysis</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{currentDeal.marketData.occupancyRate}%</p>
              <p className="text-sm text-gray-600">Occupancy Rate</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">${currentDeal.marketData.averageRentPsf}</p>
              <p className="text-sm text-gray-600">Avg Rent/SF</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{currentDeal.marketData.schoolScore}</p>
              <p className="text-sm text-gray-600">School Score</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-gray-900">{currentDeal.marketData.walkScore}</p>
              <p className="text-sm text-gray-600">Walk Score</p>
            </div>
          </div>
        </div>

        {/* Export Options */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h4 className="text-lg font-semibold text-gray-900 mb-4">Export Options</h4>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <button className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <Download className="h-5 w-5 mr-2" />
              Excel Model
            </button>
            <button className="flex items-center justify-center px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
              <FileText className="h-5 w-5 mr-2" />
              PDF Report
            </button>
            <button className="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <ExternalLink className="h-5 w-5 mr-2" />
              Generate LOI
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h2 className="text-2xl font-bold text-gray-900">New Deal Analysis</h2>
        <p className="text-gray-600 mt-1">Get AI-powered underwriting in under 30 seconds</p>
      </div>

      {isAnalyzing ? (
        <div className="bg-white p-8 rounded-lg shadow-sm border border-gray-200">
          <div className="text-center">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4 animate-pulse">
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Analyzing Deal</h3>
            <p className="text-gray-600 mb-6">{analysisSteps[analysisStep]}</p>
            
            <div className="w-full bg-gray-200 rounded-full h-2 mb-4">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-1000"
                style={{ width: `${((analysisStep + 1) / analysisSteps.length) * 100}%` }}
              ></div>
            </div>
            
            <p className="text-sm text-gray-500">
              Step {analysisStep + 1} of {analysisSteps.length}
            </p>
          </div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Property Address Input */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <MapPin className="h-5 w-5 mr-2 text-blue-600" />
              Property Address
            </h3>
            <input
              type="text"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder="Enter property address (e.g., 123 Main St, Austin, TX)"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          {/* File Upload Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FileText className="h-5 w-5 mr-2 text-green-600" />
                T12 Financial Statement
              </h3>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">Upload T12 PDF or Excel file</p>
                <p className="text-sm text-gray-500">Drag & drop or click to browse</p>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <FileText className="h-5 w-5 mr-2 text-purple-600" />
                Rent Roll
              </h3>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors cursor-pointer">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">Upload Rent Roll PDF or Excel file</p>
                <p className="text-sm text-gray-500">Drag & drop or click to browse</p>
              </div>
            </div>
          </div>

          {/* Analysis Button */}
          <div className="text-center">
            <button
              onClick={handleAnalysis}
              disabled={!address.trim()}
              className="px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors text-lg font-semibold"
            >
              🚀 Analyze Deal in 30 Seconds
            </button>
            <p className="text-sm text-gray-500 mt-2">
              Our AI will extract data, pull market comps, and generate recommendations
            </p>
          </div>
        </div>
      )}
    </div>
  );
};